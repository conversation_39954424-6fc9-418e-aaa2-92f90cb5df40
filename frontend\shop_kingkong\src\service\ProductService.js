import axios from "axios";

const ProductService = {
  getAll() {
    return axios.get("products");
  },
  getProductById(productId) {
    return axios.get(`product/${productId}`);
  },
  createProduct(productRequest) {
    return axios.post(`product/create/${productRequest}`);
  },
  updateProduct(productId, productRequest) {
    return axios.put(`product/update/${productId}/${productRequest}`);
  },
  deleteProduct(productId) {
    return axios.delete(`product/delete/${productId}`);
  },
};
